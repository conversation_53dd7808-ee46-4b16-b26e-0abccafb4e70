using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ComRemitBlazor.Models
{
    [Table("Diary_Events")]
    public class DiaryEvent
    {
        [Key]
        public int Id { get; set; }
        
        public string? Title { get; set; }
        
        public string? Description { get; set; }
        
        public DateTime? EventDate { get; set; }
        
        public DateTime? CreateDate { get; set; }
        
        public string? CreateUser { get; set; }
    }
} 