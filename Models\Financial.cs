using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ComRemitBlazor.Models
{
    [Table("Finacial")]
    public class Financial
    {
        [Key]
        [Column("sno")]
        public int Id { get; set; }
        
        [Required(ErrorMessage = "金融機關名稱為必填欄位")]
        [Display(Name = "金融機關名稱")]
        [Column("FinancialName")]
        public string? Name { get; set; }
        
        [Required(ErrorMessage = "金融機關代號為必填欄位")]
        [Display(Name = "金融機關代號")]
        [Column("AccountNo")]
        public string? Code { get; set; }
        
        [Display(Name = "金融機關住址")]
        [Column("FinancialAddr")]
        public string? Address { get; set; }
        
        [Display(Name = "金融機關類別")]
        [Column("FkindSno")]
        public int? CategoryId { get; set; }
        
        // 暫時使用計算屬性來顯示類別，稍後可以改為Join查詢
        [NotMapped]
        public string? Category 
        { 
            get 
            {
                return CategoryId switch
                {
                    1 => "國內銀行",
                    2 => "信用合作社",
                    3 => "農漁會信用部", 
                    4 => "郵局",
                    5 => "外國銀行",
                    _ => "其他"
                };
            }
        }
    }
} 