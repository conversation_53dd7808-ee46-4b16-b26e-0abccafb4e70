@page "/finunit/edit"
@page "/finunit/edit/{id:int}"
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation
@inject DatabaseInitializationService DatabaseService
@using ComRemitBlazor.Services
@using ComRemitBlazor.Models

<PageTitle>@(Id.HasValue ? "編輯" : "?��?")?��?機�?</PageTitle>

<div class="container-fluid mt-3">
    <div class="card">
        <div class="card-header">
            <h4>@(Id.HasValue ? "編輯" : "新增")金融機構資料</h4>
        </div>
        <div class="card-body">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <EditForm Model="@institution" OnValidSubmit="HandleValidSubmit">
                        <DataAnnotationsValidator />
                        <ValidationSummary class="text-danger mb-3" />

                        <div class="mb-3">
                            <label for="name" class="form-label">金融機構名稱 <span class="text-danger">*</span></label>
                            <InputText id="name" class="form-control" @bind-Value="institution.Name" />
                            <ValidationMessage For="@(() => institution.Name)" class="text-danger" />
                        </div>

                        <div class="mb-3">
                            <label for="code" class="form-label">金融機構代號 <span class="text-danger">*</span></label>
                            <InputText id="code" class="form-control" @bind-Value="institution.Code" />
                            <ValidationMessage For="@(() => institution.Code)" class="text-danger" />
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">金融機構住址</label>
                            <InputText id="address" class="form-control" @bind-Value="institution.Address" />
                            <ValidationMessage For="@(() => institution.Address)" class="text-danger" />
                        </div>

                        <div class="mb-3">
                            <label for="category" class="form-label">金融機構類別</label>
                            <InputSelect id="category" class="form-select" @bind-Value="institution.CategoryId">
                                <option value="">請選擇</option>
                                <option value="1">本國銀行</option>
                                <option value="2">信用合作社</option>
                                <option value="3">農漁會信用部</option>
                                <option value="4">郵局</option>
                                <option value="5">外國銀行</option>
                            </InputSelect>
                            <ValidationMessage For="@(() => institution.CategoryId)" class="text-danger" />
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <button type="button" class="btn btn-secondary" @onclick="GoBack">返回</button>
                            <button type="submit" class="btn btn-primary">
                                @(Id.HasValue ? "更新" : "新增")
                            </button>
                        </div>
                    </EditForm>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter]
    public int? Id { get; set; }

    private Financial institution = new Financial();

    protected override async Task OnInitializedAsync()
    {
        if (Id.HasValue)
        {
            try
            {
                // 從?實??庫??資?
                var data = await DatabaseService.GetFinancialByIdAsync(Id.Value);
                institution = data ?? new Financial();
            }
            catch (Exception)
            {
                institution = new Financial();
            }
        }
        else
        {
            institution = new Financial();
        }
    }

    private async Task HandleValidSubmit()
    {
        try
        {
            bool success = false;
            
            if (Id.HasValue)
            {
                // ?新資?
                success = await DatabaseService.UpdateFinancialAsync(institution);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "更新成功!");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "更新失敗!");
                    return;
                }
            }
            else
            {
                // ?��?資�?
                success = await DatabaseService.CreateFinancialAsync(institution);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "新增成功!");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "新增失敗!");
                    return;
                }
            }

            Navigation.NavigateTo("/finunit");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("alert", $"操作失敗：{ex.Message}");
        }
    }

    private void GoBack()
    {
        Navigation.NavigateTo("/finunit");
    }
} 
