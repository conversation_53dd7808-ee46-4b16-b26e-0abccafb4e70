@page "/payeemt"
@page "/payeemt/{encodedParams}"
@rendermode @(new InteractiveServerRenderMode(prerender: false))
@using ComRemitBlazor.Models
@using ComRemitBlazor.Services
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.JSInterop
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime
@inject PayeeService PayeeService

<PageTitle>收款人資料維護</PageTitle>

<h1>收款人資料維護</h1>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body position-relative">
                @if (isLoading)
                {
                    <div class="position-absolute top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center bg-light bg-opacity-75" style="z-index: 1000;">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">載入中...</span>
                            </div>
                            <div class="mt-2">載入中...</div>
                        </div>
                    </div>
                }
                
                <EditForm Model="@payee" OnValidSubmit="HandleValidSubmit">
                    <DataAnnotationsValidator />
                    <ValidationSummary />

                    <div class="mb-3">
                        <label for="collectName" class="form-label">收款人姓名</label>
                        <InputText id="collectName" class="form-control" @bind-Value="payee.CollectName" />
                        <ValidationMessage For="@(() => payee.CollectName)" />
                    </div>

                    <div class="mb-3">
                        <label for="collectId" class="form-label">身分證字號</label>
                        <InputText id="collectId" class="form-control" @bind-Value="payee.CollectId" />
                        <ValidationMessage For="@(() => payee.CollectId)" />
                    </div>

                    <div class="mb-3">
                        <label for="collecAcc" class="form-label">帳號</label>
                        <InputText id="collecAcc" class="form-control" @bind-Value="payee.CollecAcc" />
                        <ValidationMessage For="@(() => payee.CollecAcc)" />
                    </div>

                    <div class="mb-3">
                        <label for="collectNo" class="form-label">金融機構代號</label>
                        <InputText id="collectNo" class="form-control" @bind-Value="payee.CollectNo" />
                        <ValidationMessage For="@(() => payee.CollectNo)" />
                    </div>

                    <div class="mb-3">
                        <label for="tel" class="form-label">電話</label>
                        <InputText id="tel" class="form-control" @bind-Value="payee.Tel" />
                        <ValidationMessage For="@(() => payee.Tel)" />
                    </div>

                    <div class="mb-3">
                        <label for="zip" class="form-label">郵遞區號</label>
                        <InputText id="zip" class="form-control" @bind-Value="payee.Zip" />
                        <ValidationMessage For="@(() => payee.Zip)" />
                    </div>

                    <div class="mb-3">
                        <label for="addr" class="form-label">地址</label>
                        <InputText id="addr" class="form-control" @bind-Value="payee.Addr" />
                        <ValidationMessage For="@(() => payee.Addr)" />
                    </div>

                    <div class="mb-3">
                        <label for="belongUnit" class="form-label">所屬單位</label>
                        <InputText id="belongUnit" class="form-control" @bind-Value="payee.BelongUnit" />
                        <ValidationMessage For="@(() => payee.BelongUnit)" />
                    </div>

                    <div class="mb-3">
                        <label for="shared" class="form-label">是否共享</label>
                        <InputSelect id="shared" class="form-select" @bind-Value="payee.Shared">
                            <option value="否">否</option>
                            <option value="是">是</option>
                        </InputSelect>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary" disabled="@(isSaving || isLoading)">
                            @if (isSaving)
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                <span>儲存中...</span>
                            }
                            else
                            {
                                <span>儲存</span>
                            }
                        </button>
                        <button type="button" class="btn btn-secondary" @onclick="GoBack" disabled="@(isSaving || isLoading)">返回</button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter]
    public string? EncodedParams { get; set; }

    private Models.Payee payee = new Models.Payee();
    private bool isLoading = false;
    private bool isSaving = false;
    private string? errorMessage = null;
    private string? successMessage = null;
    private bool shouldNavigateToList = false;
    private bool isEditMode = false;

    protected override async Task OnInitializedAsync()
    {
        if (!string.IsNullOrEmpty(EncodedParams))
        {
            try
            {
                isLoading = true;
                StateHasChanged();
                
                // 解碼參數
                var decodedParams = System.Web.HttpUtility.UrlDecode(EncodedParams);
                var parts = decodedParams.Split('|');
                
                if (parts.Length == 2)
                {
                    var collectNo = parts[0];
                    var collecAcc = parts[1];
                    
                    // 從資料庫取得資料
                    var existingPayee = await PayeeService.GetPayeeByKeysAsync(collectNo, collecAcc);
                    if (existingPayee != null)
                    {
                        payee = existingPayee;
                        isEditMode = true;
                    }
                    else
                    {
                        errorMessage = "找不到指定的收款人資料！";
                        shouldNavigateToList = true;
                    }
                }
                else
                {
                    errorMessage = "無效的參數格式！";
                    shouldNavigateToList = true;
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"載入資料時發生錯誤：{ex.Message}";
                shouldNavigateToList = true;
            }
            finally
            {
                isLoading = false;
                StateHasChanged();
            }
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (!string.IsNullOrEmpty(errorMessage))
        {
            await JSRuntime.InvokeVoidAsync("alert", errorMessage);
            errorMessage = null;
            if (shouldNavigateToList)
            {
                NavigationManager.NavigateTo("/payeelist");
            }
        }
        
        if (!string.IsNullOrEmpty(successMessage))
        {
            await JSRuntime.InvokeVoidAsync("alert", successMessage);
            successMessage = null;
            if (shouldNavigateToList)
            {
                NavigationManager.NavigateTo("/payeelist");
            }
        }
    }

    private async Task HandleValidSubmit()
    {
        try
        {
            isSaving = true;
            StateHasChanged();

            bool success = false;
            
            if (isEditMode)
            {
                // 更新現有資料
                success = await PayeeService.UpdatePayeeAsync(payee);
            }
            else
            {
                // 新增資料 - 檢查帳號是否已存在
                if (!string.IsNullOrEmpty(payee.CollectNo) && !string.IsNullOrEmpty(payee.CollecAcc))
                {
                    var accountExists = await PayeeService.IsAccountExistsAsync(payee.CollectNo, payee.CollecAcc);
                    if (accountExists)
                    {
                        errorMessage = "此金融機構代號和帳號組合已存在！";
                        return;
                    }
                }
                
                // 設定建立日期為當前時間
                payee.Createdate = DateTime.Now;
                
                success = await PayeeService.AddPayeeAsync(payee);
            }

            if (success)
            {
                successMessage = isEditMode ? "更新成功！" : "新增成功！";
                shouldNavigateToList = true;
            }
            else
            {
                errorMessage = isEditMode ? "更新失敗！" : "新增失敗！";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"儲存失敗：{ex.Message}";
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private void GoBack()
    {
        NavigationManager.NavigateTo("/payeelist");
    }
} 