@page "/remitedlist"
@rendermode @(new InteractiveServerRenderMode(prerender: false))
@using ComRemitBlazor.Models
@using ComRemitBlazor.Services
@inject RemitedListService RemitedListService
@inject IJSRuntime JSRuntime

<PageTitle>已彙整清單</PageTitle>

<h3>已彙整清單</h3>

<div class="row mb-3">
    <div class="col-md-4">
        <input type="text" class="form-control" placeholder="系統編號/收款人戶名/用途說明搜尋" @bind="searchKeyword"
            @onkeypress="OnKeyPress" />
    </div>
    <div class="col-md-2">
        <button class="btn btn-primary" @onclick="Search">搜尋</button>
         <button class="btn btn-secondary" @onclick="ClearSearch">清除</button>
    </div>
</div>
@if (!string.IsNullOrEmpty(errorMessage))
{
    <div class="alert alert-danger">@errorMessage</div>
}

@if (remitedList != null)
{
    <table class="table table-hover" style="border: 1px solid #e9ecef; border-collapse: collapse;">
        <thead>
            <tr style="background-color: #f8f9fa;">
                <th style="border: 1px solid #dee2e6; padding: 10px; font-weight: 600; color: #495057;">系統編號</th>
                <th style="border: 1px solid #dee2e6; padding: 10px; font-weight: 600; color: #495057;">筆數/申請金額</th>
                <th style="border: 1px solid #dee2e6; padding: 10px; font-weight: 600; color: #495057;">前5筆明細</th>
                <th style="border: 1px solid #dee2e6; padding: 10px; font-weight: 600; color: #495057;">用途說明/彙整時間</th>
                <th style="border: 1px solid #dee2e6; padding: 10px; font-weight: 600; color: #495057;">操作</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in remitedList)
            {
                <tr>
                    <td style="border: 1px solid #e9ecef; padding: 12px; vertical-align: top;">@item.ConSno</td>
                    <td style="border: 1px solid #e9ecef; padding: 12px; vertical-align: top;">共 @item.Count 筆 <br />
                        @item.TotalAmount.ToString("F0") </td>
                    <td style="border: 1px solid #e9ecef; padding: 12px; vertical-align: top;">
                        @if (item.Details != null && item.Details.Any())
                        {
                            <table class="table table-sm" style="margin-bottom: 0;font-size:12px; border: 1px solid #c4f5d2;">
                                <thead>
                                    <tr style="background-color: #f8f9fa;">
                                        <th
                                            style="border: 1px solid #c4f5d2; padding: 8px; font-size: 0.799rem; font-weight: 500; background-color:#c4f5d2; width: 50px;">
                                            序號</th>
                                        <th
                                            style="border: 1px solid #c4f5d2; padding: 8px; font-size: 0.799rem; font-weight: 500; background-color:#c4f5d2;">
                                            收款人戶名</th>
                                        <th
                                            style="border: 1px solid #c4f5d2; padding: 8px; font-size: 0.799rem; font-weight: 500; background-color:#c4f5d2; width: 80px;">
                                            行號</th>
                                        <th
                                            style="border: 1px solid #c4f5d2; padding: 8px; font-size: 0.799rem; font-weight: 500; background-color:#c4f5d2;">
                                            帳號</th>
                                        <th
                                            style="border: 1px solid #c4f5d2; padding: 8px; font-size: 0.799rem; font-weight: 500; background-color:#c4f5d2;">
                                            金額</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @{
                                        int rowIndex = 1;
                                    }
                                    @foreach (var d in item.Details)
                                    {
                                        <tr>
                                            <td style="border: 1px solid #c4f5d2; padding: 6px; font-size: 0.799rem; text-align: center;">@rowIndex</td>
                                            <td style="border: 1px solid #c4f5d2; padding: 6px; font-size: 0.799rem;">@d.CollecName</td>
                                            <td style="border: 1px solid #c4f5d2; padding: 6px; font-size: 0.799rem; text-align: center;">@d.CollectNo</td>
                                            <td style="border: 1px solid #c4f5d2; padding: 6px; font-size: 0.799rem;">@d.CollecAcc</td>
                                            <td style="border: 1px solid #c4f5d2; padding: 6px; font-size: 0.799rem;">@d.RemitPrice</td>
                                        </tr>
                                        rowIndex++;
                                    }
                                </tbody>
                            </table>
                        }
                        else
                        {
                            <span class="text-muted">無明細資料</span>
                        }
                    </td>
                    <td style="border: 1px solid #e9ecef; padding: 12px; vertical-align: top;">@item.ConMemo <br /> 【
                        @item.ConDate 】</td>
                    <td style="border: 1px solid #e9ecef; padding: 12px; vertical-align: top;">
                        <button class="btn btn-info btn-sm" @onclick="() => Print(item.ConSno)">列印</button>
                        <button class="btn btn-success btn-sm" @onclick="() => Export(item.ConSno)">匯出</button>
                        <button class="btn btn-secondary btn-sm" @onclick="() => Copy(item.ConSno)">複製</button>
                        <button class="btn btn-danger btn-sm" @onclick="() => Delete(item.ConSno)">刪除</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
    <!-- 分頁 -->
    @if (totalPages > 1)
    {
        <div style="display: flex; justify-content: center; align-items: center; margin-top: 20px; gap: 10px;">
            <!-- 第1頁 -->
            <button class="btn @(pageIndex == 1 ? "btn-secondary" : "btn-outline-primary") btn-sm"
                @onclick="@(async () => await GoToFirstPage())" disabled="@(pageIndex == 1)">
                第1頁
            </button>

            <!-- 上1頁 -->
            <button class="btn @(pageIndex == 1 ? "btn-secondary" : "btn-outline-primary") btn-sm"
                @onclick="@(async () => await GoPreviousPage())" disabled="@(pageIndex == 1)">
                上1頁
            </button>

            <!-- 目前第N頁 -->
            <span class="badge bg-primary fs-6 px-3 py-2">
                目前第 @pageIndex 頁 (共 @totalPages 頁)
            </span>

            <!-- 下1頁 -->
            <button class="btn @(pageIndex == totalPages ? "btn-secondary" : "btn-outline-primary") btn-sm"
                @onclick="@(async () => await GoNextPage())" disabled="@(pageIndex == totalPages)">
                下1頁
            </button>

            <!-- 最後1頁 -->
            <button class="btn @(pageIndex == totalPages ? "btn-secondary" : "btn-outline-primary") btn-sm"
                @onclick="@(async () => await GoToLastPage())" disabled="@(pageIndex == totalPages)">
                最後1頁
            </button>
        </div>
    }
}

@code {
    private string searchKeyword = string.Empty;
    private string errorMessage = string.Empty;
    private List<RemitedListViewModel>? remitedList;
    private int pageIndex = 1;
    private int pageSize = 20;
    private int totalPages = 1;
    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }


    private async Task LoadData()
    {
        var result = await RemitedListService.GetPagedListAsync(searchKeyword, pageIndex, pageSize);
        remitedList = result.Items;
        totalPages = result.TotalPages;
    }

    private async Task Search()
    {
        pageIndex = 1;
        await LoadData();
    }

    private async Task ClearSearch()
    {
        searchKeyword = string.Empty;
        pageIndex = 1;
        await LoadData();
    }

    private void GoToPage(int page)
    {
        try
        {
            if (page >= 1 && page <= totalPages)
            {
                pageIndex = page;
                StateHasChanged();
                Console.WriteLine($"跳轉到第 {page} 頁，共 {totalPages} 頁");
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"換頁時發生錯誤: {ex.Message}";
            StateHasChanged();
        }
    }

    private async Task GoToFirstPage()
    {
        GoToPage(1);
        await LoadData();
    }

    private async Task GoPreviousPage()
    {
        if (pageIndex > 1)
        {
            GoToPage(pageIndex - 1);
            await LoadData();
        }
    }

    private async Task GoNextPage()
    {
        if (pageIndex < totalPages)
        {
            GoToPage(pageIndex + 1);
            await LoadData();
        }
    }

    private async Task GoToLastPage()
    {
        GoToPage(totalPages);
        await LoadData();
    }

    // 保留舊方法以防其他地方有使用
    private async Task PrevPage()
    {
        await GoPreviousPage();
    }

    private async Task NextPage()
    {
        await GoNextPage();
    }



    private async Task Print(int? conSno)
    {
        if (conSno.HasValue)
        {
            try
            {
                var (pdfBytes, filename) = await RemitedListService.PrintPdfAsync(conSno.Value);
                if (pdfBytes != null && pdfBytes.Length > 0)
                {
                    var base64 = Convert.ToBase64String(pdfBytes);
                    await JSRuntime.InvokeVoidAsync("downloadPdfFile", filename, base64);
                }
                else
                {
                    // 顯示錯誤訊息
                    await JSRuntime.InvokeVoidAsync("alert", "無法生成PDF文件，請檢查數據是否正確。");
                }
            }
            catch (Exception ex)
            {
                // 顯示詳細錯誤訊息
                await JSRuntime.InvokeVoidAsync("alert", $"列印時發生錯誤：{ex.Message}");
                Console.WriteLine($"Print error: {ex}");
            }
        }
        else
        {
            await JSRuntime.InvokeVoidAsync("alert", "無效的編號");
        }
    }

    private async Task Export(int? conSno)
    {
        if (conSno.HasValue)
        {
            var txt = await RemitedListService.ExportTxtAsync(conSno.Value);
            if (!string.IsNullOrEmpty(txt))
            {
                var bytes = System.Text.Encoding.UTF8.GetBytes(txt);
                var base64 = Convert.ToBase64String(bytes);
                await JSRuntime.InvokeVoidAsync("downloadFile", $"RemitedList_{conSno}.txt", base64);
            }
        }
    }

    private async Task Copy(int? conSno)
    {
        if (conSno.HasValue)
        {
            var newConSno = await RemitedListService.CopyByConSnoAsync(conSno.Value);
            if (newConSno.HasValue)
            {
                searchKeyword = string.Empty;
                pageIndex = 1;
                await LoadData();
            }
        }
    }

    private async Task Delete(int? conSno)
    {
        if (conSno.HasValue)
        {
            try
            {
                // 顯示確認對話框
                var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", $"確定要刪除系統編號 {conSno} 的資料嗎？\n此操作無法復原！");

                if (confirmed)
                {
                    await JSRuntime.InvokeVoidAsync("console.log", $"用戶確認刪除 ConSno: {conSno}");

                    // 執行刪除
                    await RemitedListService.DeleteByConSnoAsync(conSno.Value);

                    // 重新載入資料
                    await LoadData();
                    errorMessage = string.Empty;

                    // 顯示成功訊息
                    await JSRuntime.InvokeVoidAsync("alert", "刪除成功！");
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("console.log", "用戶取消刪除操作");
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"刪除失敗：{ex.Message}";
                await JSRuntime.InvokeVoidAsync("alert", $"刪除失敗：{ex.Message}");
            }
        }
    }

    private async Task OnKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await Search();
        }
    }
}

<script>
    function downloadFile(filename, content) {
        const element = document.createElement('a');
        element.setAttribute('href', 'data:text/plain;base64,' + content);
        element.setAttribute('download', filename);
        element.style.display = 'none';
        document.body.appendChild(element);
        element.click();
        document.body.removeChild(element);
    }
    function downloadPdfFile(filename, content) {
        const element = document.createElement('a');
        element.setAttribute('href', 'data:application/pdf;base64,' + content);
        element.setAttribute('download', filename);
        element.style.display = 'none';
        document.body.appendChild(element);
        element.click();
        document.body.removeChild(element);
    }
</script>
