using ComRemitBlazor.Components;
using ComRemitBlazor.Models;
using ComRemitBlazor.Services;
using Microsoft.EntityFrameworkCore;
using QuestPDF.Infrastructure;

var builder = WebApplication.CreateBuilder(args);

// 設定 QuestPDF 授權
QuestPDF.Settings.License = LicenseType.Community;

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

// 註�?資�?庫�?下�?
builder.Services.AddDbContext<ComRemitDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("ComRemitConnectionString")));

// 註�??��?
builder.Services.AddScoped<RemitService>();
builder.Services.AddScoped<DatabaseInitializationService>();
builder.Services.AddScoped<BarcodeService>();
builder.Services.AddScoped<PayeeService>();
builder.Services.AddScoped<RemitedListService>();

var app = builder.Build();

// ?��??��??�庫
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    try
    {
        var dbInit = services.GetRequiredService<DatabaseInitializationService>();
        var canConnect = await dbInit.TestConnectionAsync();
        if (canConnect)
        {
            await dbInit.EnsureFinancialTableExistsAsync();
        }
    }
    catch (Exception)
    {
        // 資�?庫�?始�?失�?，�?使用?�設設�?
    }
}

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

// ?��?移除 HTTPS ?��??�以?��?端口?�誤
// app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseRouting();

app.UseAntiforgery();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

app.Run();
