-- 創建測試數據用於測試 PDF 生成功能
-- 這些數據將用於測試郵局格式的 PDF 生成

-- 首先檢查是否已有測試數據
IF NOT EXISTS (SELECT 1 FROM RemitedList WHERE ConSno = 1001)
BEGIN
    -- 插入郵局格式測試數據 (CollectNo = '7000021')
    INSERT INTO RemitedList (CollectNo, CollecAcc, CollecName, CollectId, RemitPrice, RemitMemo, ConSno, ConPer, ConUnit, ConDate, kindno, CashDate, BatchNum, IfFee, ConMemo)
    VALUES 
    ('7000021', '01234567890123', '張三', 'A123456789', 5000, '113全國學生音樂比賽獎金', 1001, '承辦人員', '宜蘭縣政府', GETDATE(), 1, GETDATE(), '1131201001', '否', '測試用途'),
    ('7000021', '01234567890124', '李四', 'B987654321', 3000, '113全國學生音樂比賽獎金', 1001, '承辦人員', '宜蘭縣政府', GETDATE(), 1, GETDATE(), '1131201001', '是', '測試用途'),
    ('7000021', '01234567890125', '王五', 'C456789123', 4500, '113全國學生音樂比賽獎金', 1001, '承辦人員', '宜蘭縣政府', GETDATE(), 1, GETDATE(), '1131201001', '否', '測試用途'),
    ('7000021', '01234567890126', '趙六', 'D789123456', 2800, '113全國學生音樂比賽獎金', 1001, '承辦人員', '宜蘭縣政府', GETDATE(), 1, GETDATE(), '1131201001', '是', '測試用途'),
    ('7000021', '01234567890127', '錢七', 'E123789456', 6200, '113全國學生音樂比賽獎金', 1001, '承辦人員', '宜蘭縣政府', GETDATE(), 1, GETDATE(), '1131201001', '否', '測試用途');
END

-- 插入台銀格式測試數據 (CollectNo != '7000021')
IF NOT EXISTS (SELECT 1 FROM RemitedList WHERE ConSno = 1002)
BEGIN
    INSERT INTO RemitedList (CollectNo, CollecAcc, CollecName, CollectId, RemitPrice, RemitMemo, ConSno, ConPer, ConUnit, ConDate, kindno, CashDate, BatchNum, IfFee, ConMemo)
    VALUES 
    ('0040001', '12345678901234', '陳八', 'F456123789', 7500, '113全國學生音樂比賽獎金', 1002, '承辦人員', '宜蘭縣政府', GETDATE(), 1, GETDATE(), '1131201002', '否', '測試用途'),
    ('0040001', '12345678901235', '林九', 'G789456123', 4200, '113全國學生音樂比賽獎金', 1002, '承辦人員', '宜蘭縣政府', GETDATE(), 1, GETDATE(), '1131201002', '是', '測試用途'),
    ('0040001', '12345678901236', '黃十', 'H123456789', 5800, '113全國學生音樂比賽獎金', 1002, '承辦人員', '宜蘭縣政府', GETDATE(), 1, GETDATE(), '1131201002', '否', '測試用途');
END

-- 查詢測試數據
SELECT 'Test Data Created Successfully' as Message;
SELECT ConSno, COUNT(*) as RecordCount, SUM(RemitPrice) as TotalAmount, 
       CASE WHEN CollectNo = '7000021' THEN '郵局格式' ELSE '台銀格式' END as FormatType
FROM RemitedList 
WHERE ConSno IN (1001, 1002)
GROUP BY ConSno, CollectNo;
